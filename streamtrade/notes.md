# Tahap 1
aku ingin membuat sebuat platform trading yang dapat melakukan backtesting dan live trading dengan ketentuan sebagai berikut :
pertama aku ingin menggunkan python serta library yang dibutuhkan dalam membuatnya.
kita akan bekerja pada direktori `streamtrade` untuk project ini jadi jangan buat file diluar direktori ini.

fitur-fitur yang mungkin ada :
1. data manager
2. chart viewer
3. indikator manager
4. strategi manager
5. backtesting
6. live trading
7. paper trading
8. risk management
9. reporting
10. optimization
11. deployment

untuk pertama kita akan membuat fitur-fitur yang berhubungan dengan backtesting, yaitu :
1. data manager
   a. data kita ambil dengan cara scanning direktori `histdata/MT/M1`
   b. pada direktori tersebut terdapat folder-folder dengan nama pair nya yang berisikan sub folder lagi dengan format tahun/file_tahunan.csv atau tahun/bulan/file_bulanan.csv
   c. silahkan cek struktur data pada direktori tersebut
   d. spesifikasi file csv dapat dilihat pada file `histdata/histdata.com_spec.md` data ini adalah data hasil download dari histdata.com
   e. saya ingin nanti dapat menggunakan data ini untuk chart viewer dan backtesting.
   f. namun mungkin masalahnya adalah data terlalu besar, perlu didapatkan solusi untuk ini. karena kemungkinan nanti akan ada penambahan data tahun-tahun sebelumnya dan data terbaru.
2. chart viewer dan indikator
   a. kita akan menggunakan library `plotly` (atau menurut anda yang terbagus) untuk membuat chart viewer, di utamakan yang dapat menghandle data yang besar dan dapat menampilkan indikator secara dinamis, serta dapat di kustomisasi.
   b. chart viewer ini akan menampilkan data dari data manager
   c. chart viewer ini akan menampilkan chart dari pair yang dipilih
   d. chart viewer ini akan menampilkan chart dari beberapa time frame yang dipilih, terkait dengan timeframe buat fungsi yang dapat otomatis mengkonversi dan validasi timeframe yang dipilih, karena mengingat kita hanya punya timeframe 1m dan kita tidak ingin mengkonversi ke dalam bentuk file.
   e. chart viewer ini akan menampilkan chart dari beberapa indikator yang dipilih dan dapat di toggle on/off, terkait dengan indikator buatlah fungsi yang dapat mengambil indikator dari library `ta-lib` atau `pandas-ta` atau `tulip` (atau menurut anda yang terbagus) dan dapat di konfigurasi sesuai dengan kebutuhan, misal indikator MACD dapat dikonfigurasi dengan fast, slow, signal, dll.
   f. indikator juga dapat kita buat custom indikator sesuai kebutuhan pada direktori khusus, dan buatkan class serta fungsi untuk mengambil indikator tersebut secara modular plug and play.
   g. setiap indikator akan mereturn variable dan menerima parameter input dari user sesuai dengan indikator yang kita buat, dan ini secara otomatis/dynamic tampil pada frontend/GUI
   h. pastikan semua indikator dapat tampil pada chart.
   i. chart perlu untuk ditampilkan secara dinamis ukurannya mengikuti parent element atau layar.
3. GUI/Frontend
   a. kita akan menggunakan library `streamlit` untuk membuat frontend, karena lebih mudah untuk membuat GUI dan dapat di deploy dengan mudah.
   b. pastikan semua fitur yang dibutuhkan sudah ada pada frontend, termasuk untuk melakukan konfigurasi dan menampilkan hasil output.
   c. pastikan semua fitur dapat diakses dengan mudah dan intuitif.

# Tahap 2

# 1. Timeframe Conversion Strategy

sebelumnya saya ingin mengerangkan bahwa tadi saya salah, default waktu/timezone data dari histdata.com adalat Eastern Standard Time (EST) time-zone WITHOUT Day Light Savings adjustments seperti yang ada pada file histdata.com_spec.md

Kemudian begini :
karena data kita adalah EST tanpa DST, maka offset 5 jam. Maka dari itu default Timezone kita adalah **UTC-5**.
- Buat Pengaturan **Timezone**, Default : **UTC-5**
- Buat Pengaturan **Display Timezone**, Default : **Asia/Jakarta**
- Buat Pengaturan **Market Open**, Default : **21:00**

dibawah ini semua waktu dalam UTC,
lalu dalam waktu sehari :
- Open : 21:00 | candle 1m pertama adalah candle 21:00 kalau tidak ada GAP, jika ada pakai candle berikutnya
- Close : 20:59 | candle 1m terakhir adalah candle 20:59 atau mungkin kurang, bisa jadi 20:44 karna GAP atau market tutup lebih cepat

jadi candle - candle timeframe besarnya mengikuti, contoh

candle 4H dalam sehari adalah
1. 21:00 -> 01:00 (candle 1m = 21:00 -> 00:59), atau bisa saja 22:05-00:58 misalnya
2. 01:00 -> 05:00 (candle 1m = 01:00 -> 04:59), sama seperti yang di atas jika ada gap
3. 05:00 -> 09:00 (candle 1m = 05:00 -> 08:59), sama seperti yang di atas jika ada gap
4. 09:00 -> 13:00 (candle 1m = 09:00 -> 12:59), sama seperti yang di atas jika ada gap
5. 13:00 -> 17:00 (candle 1m = 13:00 -> 16:59), sama seperti yang di atas jika ada gap
6. 17:00 -> tutup (candle 1m = 17:00 -> tutup), kenapa ini sampai tutup, bisa jadi hanya 3 jam isinya karena pasar tutup, misal untuk pair XAU, yang penting ketika 21:00 = hari baru

kemudian untuk timeframe lainnya sama, ukur mulainya dari 21:00
misal daily dari 21:00 sampai selesai
kemudian weekly dari 21:00 hari senin sampai selesai candle hari jum'at


jika data candle 1m "tidak ada (GAP)" maka sesuaikan dengan rentang waktu itu open dan close timeframe atasnya.

# 2. Insufficient Data Handling
buat **pengaturan** tentang ini, apakah load otomatis di background atau tampilkan kosong dengan warning/pesan.

# 3. User Experience
berdasarkan nomor 2 di atas.

# 4. Data Loading Strategy
saat ini kita punya 3 yaitu, **Last N Canldes, Date Range, All Available**
ganti yang Last N Candles jadi **N Days Back** dengan default 5 dan tidak boleh kurang dari 1

# Tambahan

pada pengaturan user tadi tambahkan pengaturan :
**Max Candles to Load** (number) default 144000 - sebelumnya kita punya pengaturan hardcode max bar = 10000
**Cache all TF on load** (switch) default false, jika true, maka saat load, semua jenis timeframe akan di cache
**Timeframes** (checkbox/multi select) default [M1, M5, M15, H1, H4, D1] jadi TF M30, W1, MN1 tidak di cache saat load, namun bisa di load nanti jika dibutuhkan jika enable pada settingan ini.

## switch timeframe, load, konversi dan cache candle :
- user load data awal sebelumnya adalah 100 hari yang lalu dengan pilihan TF 4H
- load data 1m 100 hari yang lalu, cache kemudian konversi ke 4H lalu cache juga data TF 4H
- tampilkan data 4H pada chart dengan limit "max candles to load" yang ada pada pengaturan
- kemudian ketika user switch timeframe, gunakan data 1m yang sudah ada di cache untuk konversi ke timeframe yang baru
- lalu cache juga data TF baru tersebut, kemudian tampilkan data pada chart dengan limit "max candles to load" yang ada pada pengaturan
yang artinya settingan "max candles to load" ini berlaku untuk semua timeframe. dan berlaku untuk melilimit bar yang ditampilkan di chart, bukan yang di load dan di cache.

## Cache persistance
- untuk cache sekarang kita pakai saja tanpa database, kita gunakan "Smart Disk Cache"
- untuk LRU eviction pada settingan buat settingan "**Max Cache Size**" dengan default 5GB


### Next Steps (Optional)

Jika ingin enhancement lebih lanjut:
**Cache persistence**: Simpan cache ke disk
**Smart preloading**: Pre-load data untuk timeframes populer
**User preferences**: Simpan preferensi candle count per timeframe
**Progressive loading**: Load data bertahap untuk timeframes besar

Optimasi yang bisa dilakukan untuk indikator:
**Incremental caching**: Cache per indikator, bukan per kombinasi
**Lazy calculation**: Hitung indikator hanya saat dibutuhkan
**Smart invalidation**: Hanya recalculate yang affected

Fokus aplikasi tentang chart :
backtesting - menampilkan semua trade plus indikator dan plot trade