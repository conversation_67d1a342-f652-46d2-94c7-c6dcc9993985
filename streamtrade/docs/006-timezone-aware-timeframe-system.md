# Phase 5: Timezone-Aware Timeframe System & Smart Caching

**Date**: 2025-06-27  
**Status**: 📋 Planning Phase  
**Priority**: High  
**Complexity**: Advanced  

## 1. Proposal Text

### Timeframe Conversion Strategy

sebelumnya saya ingin mengerangkan bahwa tadi saya salah, default waktu/timezone data dari histdata.com adalat Eastern Standard Time (EST) time-zone WITHOUT Day Light Savings adjustments seperti yang ada pada file histdata.com_spec.md

Kemudian begini :
karena data kita adalah EST tanpa DST, maka offset 5 jam. Maka dari itu default Timezone kita adalah **UTC-5**.
- Buat Pengaturan **Timezone (Data)**, Default : **UTC-5**
- Buat Pengaturan **Timezone (Display)**, Default : **UTC+7** (Asia/Jakarta)
- Buat Pengaturan **Market Open (Forex)**, Default : **16:00**
- Buat Pengaturan **Market Open (Non Forex)**, Default : **17:00**
- mungkin kita juga perlu pengaturan **Non Forex Symbols**, Default : **XAUUSD, SPXUSD, NSXUSD** (kalau bisa multi select dengan display pairs yang tersedia)

dengan pengaturan di atas, sebenarnya kita tidak perlu konversi waktu pada data.
kita cukup gunakan data yang ada dengan menganggap bahwa data yang ada adalah sama dengan yang ada pada settingan kita di atas.

#### jadi begini misal untuk settingan di atas **UTC-5**

##### 1. Forex dan market open 16:00 (**Untuk Forex**),
lalu dalam waktu sehari :
- Open  : 16:00 | candle 1m pertama adalah candle 16:00 kalau tidak ada GAP, jika ada pakai candle berikutnya  
- Close : 15:59 | candle 1m terakhir adalah candle 15:59 atau mungkin kurang, bisa jadi 15:44 karena GAP atau market tutup lebih cepat  

jadi candle–candle timeframe besarnya mengikuti, contoh :
candle 4H dalam sehari adalah  
1. 16:00 → 20:00 (candle 1m = 16:00 → 19:59), atau bisa saja 17:05–19:58 misalnya  
2. 20:00 → 00:00 (candle 1m = 20:00 → 23:59), sama seperti di atas jika ada gap  
3. 00:00 → 04:00 (candle 1m = 00:00 → 03:59), sama seperti di atas jika ada gap  
4. 04:00 → 08:00 (candle 1m = 04:00 → 07:59), sama seperti di atas jika ada gap  
5. 08:00 → 12:00 (candle 1m = 08:00 → 11:59), sama seperti di atas jika ada gap  
6. 12:00 → tutup (candle 1m = 12:00 → tutup), kenapa ini sampai tutup? bisa jadi hanya 3 jam isinya karena pasar tutup, Yang penting ketika 16:00 = hari baru  

kemudian untuk timeframe lainnya sama, mulainya dari 16:00  
misal daily dari 16:00 sampai selesai, atau weekly dari 16:00 hari Senin sampai selesai candle hari Jumat
jika data candle 1m "tidak ada (GAP)" maka sesuaikan dengan rentang waktu itu open dan close timeframe atasnya.

##### 2. Kemudian untuk pair non-forex dan market open 17:00, misalnya XAUUSD (gold) dan SPXUSD (S&P 500) dan NSXUSD (Nasdaq)  
kita pakai pengaturan "Non Forex Market Open" yang default 17:00, maka :
- Open  : 17:00 | candle 1m pertama adalah candle 17:00 kalau tidak ada pakai candle berikutnya  
- Close : 16:59 | candle 1m terakhir adalah candle 16:59 atau mungkin kurang, bisa jadi 16:44 karena GAP atau market tutup lebih cepat  

kemudian untuk timeframe besarnya mengikuti, contoh :
candle 4H dalam sehari adalah  
1. 17:00 -> 21:00 (candle 1m = 17:00 -> 20:59), atau bisa saja 17:05-20:58 misalnya  
2. 21:00 -> 01:00 (candle 1m = 21:00 -> 00:59), sama seperti yang di atas jika ada gap  
3. 01:00 -> 05:00 (candle 1m = 01:00 -> 04:59), sama seperti yang di atas jika ada gap  
4. 05:00 -> 09:00 (candle 1m = 05:00 -> 08:59), sama seperti yang di atas jika ada gap  
5. 09:00 -> 13:00 (candle 1m = 09:00 -> 12:59), sama seperti yang di atas jika ada gap  
6. 13:00 -> tutup (candle 1m = 13:00 -> tutup), kenapa ini sampai tutup? bisa jadi hanya 3 jam isinya karena pasar tutup, Yang penting ketika 17:00 = hari baru

kemudian untuk timeframe lainnya sama, mulainya dari 17:00  
misal daily dari 17:00 sampai selesai, atau weekly dari 17:00 hari Senin sampai selesai candle hari Jumat
jika data candle 1m "tidak ada (GAP)" maka sesuaikan dengan rentang waktu itu open dan close timeframe atasnya.

### Insufficient Data Handling
buat **pengaturan** tentang ini,
"**Insufficient Data Behavior**" choice antara apakah "Load otomatis di background" atau "Tampilkan kosong dengan warning/pesan".

### Cache persistence
- untuk cache sekarang kita pakai saja tanpa database, kita gunakan "**Smart Disk Cache**" `Parquet files + metadata index + LRU eviction`
- untuk LRU eviction pada settingan buat settingan "**Max Cache Size**" dengan default 10GB

### Data Loading Strategy
saat ini kita punya 3 yaitu, **Last N Canldes, Date Range, All Available**
ganti yang Last N Candles jadi **N Days Back** dengan default 5 dan tidak boleh kurang dari 1

pada pengaturan user tadi tambahkan pengaturan :
- **Max Candles to Load** (number) default 200000
- **Max Canldes to Display** (Number) default 15000 (sebelumnya kita punya pengaturan hardcode max bar = 10000)
- **Eanble Timeframes** (checkbox/multi select) default [M1, M5, M15, H1, H4, D1], jadi TF M30, W1, MN1 tidak di gunakan secara default.
- **Cache all TF on load** (switch) default false, jika true, maka saat load, semua jenis timeframe yang di enable pada settingan di atas akan di cache.

#### switch timeframe, load, konversi dan cache candle :
- user load data awal sebelumnya adalah 100 hari yang lalu dengan pilihan TF 4H
- load data 1m 100 hari yang lalu, cache data 1m, kemudian konversi ke 4H, lalu cache juga data TF 4H
- tampilkan data 4H pada chart dengan limit "max candles to display" yang ada pada pengaturan
- kemudian ketika user switch timeframe
- gunakan data 1m yang sudah ada di cache untuk konversi ke timeframe yang baru, lalu cache juga data TF baru tersebut
- kemudian tampilkan data pada chart dengan limit "max candles to display" yang ada pada pengaturan
- yang artinya settingan "max candles to display" ini berlaku untuk semua timeframe. dan berlaku untuk melilimit bar yang ditampilkan di chart, bukan yang di load dan di cache.
- sedangkan "max candles to load" ini berlaku untuk melimit jumlah data yang di load dari file, bukan yang di cache atau yang di tampilkan di chart.

### Indikator cache strategy
karena kita menggunakan disk untuk cache, saya tidak tau ini bisa dilakukan atau tidak, namun saya ingin :
- saat user mengambahkan indikator maka kalkulasi dan cahe indikator ini dibuat terpisah per indikator.
- untuk data yang bersifat style (warna, garis, dll.) simpan di cache terpisah per indikator.
- kalkulasi dan cache ulang hanya indikator yang terdampak saja.
- begitu juga dengan saat menghapus, hanya indikator yang terdampak saja yang dihapus dari cache dan pastikan hapus cache kalkulasi dan style nya juga.
namun artinya cache data dan indikator harus terhubung agar misalnya ketika data dihapus maka semua indikator yang terdampak juga ikut terhapus dari cache.
saya belum tau apakah ini bisa dilakukan dengan menggunakan disk cache kita.

### Pengaturan
karena kita tidak menggunakan database, maka semua pengaturan user disimpan pada sebuah file dengan format file dan caranya yang menurut anda yang terbaik.

## 2. Prompt Inisialisasi

```
pelajari file notes.md baris 44:135 silahkan dipelajari. ini bagus atau tidak, cukup atau tidak, sekiranya cukup tanyakan untuk langkah selanjutnya.
```

## 3. Prompt Kedua

```
buat susunan nya dalam sebuah plans. update `/streamtrade/plans.md` kita, tambahkan semua informasi step-by-step pada plans, buat Phase 5 (serta sub-sub nya) untuk semua tahapan ini (Kita lewati dulu phase 4). tapi jangan lupa buatkan dokumentasi khusus untuk pekerjaan tahap ke 5 ini dalam sebuah file pada folder `/streamtrade/docs` yang berisikan :
1. Proposal text
2. Prompt Inisialisasi
3. Prompt kedua
4. Hasil analisa dan pembelajaran
```

## 4. Hasil Analisa dan Pembelajaran

### 4.1 Keadaan Program Saat Ini (Sebelumnya)

**Current State Analysis:**
Platform Lionaire saat ini sudah memiliki foundation yang solid dengan Phase 1 dan Phase 2 completed:

**✅ Yang Sudah Ada:**
- Complete data management system (DataLoader, TimeframeConverter, DataManager)
- Interactive visualization dengan Plotly charts
- Technical indicators system (8 built-in + framework)
- Professional Streamlit GUI
- Basic caching system (memory-based)
- Timeframe switching functionality

**❌ Masalah Fundamental yang Teridentifikasi:**

#### 1. **Timeframe Conversion Logic yang Salah**
```python
# Current (WRONG): Math-based conversion
h4_candles = m1_data_size // 240  # 240 M1 = 1 H4
d1_candles = m1_data_size // 1440  # 1440 M1 = 1 D1

# Problem: Tidak mempertimbangkan session boundaries dan gap
```

**Contoh Masalah:**
- User load 12 H1 → Switch ke H4 → Dapat 3 candles (mungkin salah)
- User load 12 H1 → Switch ke D1 → Dapat 2 candles (jelas salah!)
- User load 12 H1 → Switch ke M5 → Dapat 12 candles (seharusnya ~144)

#### 2. **Tidak Ada Timezone Awareness**
- Data histdata.com dalam EST (UTC-5) tanpa DST
- Tidak ada market session boundaries yang proper
- Forex vs Non-Forex instruments tidak dibedakan
- Timeframe boundaries tidak sesuai dengan trading session

#### 3. **Cache System Limitations**
- Cache hanya di memory (hilang saat browser refresh)
- Tidak ada persistence across sessions
- Tidak ada LRU eviction (memory leak potential)
- Indicator cache tidak terpisah (inefficient)

#### 4. **Data Loading Strategy Suboptimal**
- "Last N Candles" tidak predictable untuk user
- Tidak ada separation antara load vs display limits
- Timeframe switching reload semua data dari file
- Tidak ada configurable timeframe enable/disable

### 4.2 Masalah yang Ingin Diatasi

#### **1. Session-Aware Timeframe Conversion**
**Problem**: Timeframe boundaries tidak sesuai dengan real trading sessions
**Solution**:
```
Forex: 16:00 daily boundary (UTC-5)
H4 Sessions: 16:00→20:00, 20:00→00:00, 00:00→04:00, 04:00→08:00, 08:00→12:00, 12:00→Close

Non-Forex: 17:00 daily boundary (UTC-5)
H4 Sessions: 17:00→21:00, 21:00→01:00, 01:00→05:00, 05:00→09:00, 09:00→13:00, 13:00→Close
```

#### **2. Gap-Aware Conversion**
**Problem**: Sistem tidak handle missing M1 data dengan baik
**Solution**: Flexible session boundaries yang adjust ke actual data availability
```
Ideal: 16:00→20:00 (240 M1 candles)
Reality: 17:05→19:58 (jika ada gap di awal/akhir)
```

#### **3. Smart Disk Cache System**
**Problem**: Memory cache tidak persistent dan tidak scalable
**Solution**: Parquet-based disk cache dengan LRU eviction
```
Cache Structure:
├── data/
│   ├── m1_base/          # M1 base data (Parquet)
│   ├── timeframes/       # Converted TF data (Parquet)
│   └── indicators/       # Per-indicator calculations (Parquet)
├── metadata/
│   ├── index.json        # Cache index
│   └── lru_tracker.json  # LRU eviction data
└── settings/
    └── user_config.json  # User preferences
```

#### **4. Time-Based Data Loading**
**Problem**: "Last N Candles" tidak intuitive dan tidak predictable
**Solution**: "N Days Back" dengan clear separation
```
Load Strategy: 5 days back (configurable, min: 1)
Max Candles to Load: 200,000 (from files)
Max Candles to Display: 15,000 (on chart)
```

### 4.3 Rencana Implementasi

#### **Phase 5.1: Core Timezone & Session System (Sprint 5)**

**5.1.1 User Settings System**
```python
# File: streamtrade/config/user_settings.py
class UserSettings:
    def __init__(self):
        self.settings_file = Path.home() / '.lionaire' / 'settings.json'
        self.default_settings = {
            'timezone': {
                'data_timezone': 'UTC-5',
                'display_timezone': 'UTC+7'
            },
            'market_sessions': {
                'forex_open': '16:00',
                'non_forex_open': '17:00',
                'non_forex_symbols': ['XAUUSD', 'SPXUSD', 'NSXUSD']
            },
            'data_loading': {
                'days_back_default': 5,
                'max_candles_load': 200000,
                'max_candles_display': 15000,
                'enabled_timeframes': ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'],
                'cache_all_tf_on_load': False
            },
            'cache': {
                'max_cache_size_gb': 10,
                'insufficient_data_behavior': 'show_warning'  # or 'auto_load'
            }
        }
```

**5.1.2 Session-Aware Timeframe Converter**
```python
# File: streamtrade/data/session_aware_converter.py
class SessionAwareConverter:
    def __init__(self, user_settings):
        self.settings = user_settings

    def get_session_boundaries(self, pair: str, date: datetime) -> dict:
        """Get market session boundaries for specific pair and date."""
        is_forex = pair not in self.settings.market_sessions.non_forex_symbols
        market_open = self.settings.market_sessions.forex_open if is_forex else self.settings.market_sessions.non_forex_open

        # Calculate H4 session boundaries
        sessions = self._calculate_h4_sessions(market_open, date)
        return sessions

    def convert_with_sessions(self, m1_data: pd.DataFrame, target_tf: str, pair: str) -> pd.DataFrame:
        """Convert M1 data to target timeframe using session boundaries."""
        # Implementation with gap-aware session handling
```

**5.1.3 N Days Back Data Loading**
```python
# File: streamtrade/data/enhanced_data_manager.py
class EnhancedDataManager(DataManager):
    def load_n_days_back(self, pair: str, timeframe: str, days_back: int = 5) -> pd.DataFrame:
        """Load data based on days back instead of candle count."""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        # Load M1 data for the time range
        m1_data = self.data_loader.load_data_range(pair, start_date, end_date)

        # Convert using session-aware converter
        if timeframe == 'M1':
            return m1_data
        else:
            return self.session_converter.convert_with_sessions(m1_data, timeframe, pair)
```

#### **Phase 5.2: Smart Disk Cache System (Sprint 6)**

**5.2.1 Parquet-Based Cache**
```python
# File: streamtrade/cache/disk_cache.py
class SmartDiskCache:
    def __init__(self, cache_dir: Path, max_size_gb: int = 10):
        self.cache_dir = cache_dir
        self.max_size_gb = max_size_gb
        self.metadata = CacheMetadata(cache_dir / 'metadata')

    def store_m1_data(self, pair: str, data: pd.DataFrame) -> str:
        """Store M1 base data in Parquet format."""
        cache_key = f"m1_{pair}_{data.index[0].date()}_{data.index[-1].date()}"
        file_path = self.cache_dir / 'data' / 'm1_base' / f"{cache_key}.parquet"
        data.to_parquet(file_path, compression='snappy')
        self.metadata.add_entry(cache_key, file_path, 'm1_base')
        return cache_key

    def store_timeframe_data(self, pair: str, timeframe: str, data: pd.DataFrame, m1_cache_key: str) -> str:
        """Store converted timeframe data."""
        cache_key = f"{timeframe}_{pair}_{data.index[0].date()}_{data.index[-1].date()}"
        file_path = self.cache_dir / 'data' / 'timeframes' / f"{cache_key}.parquet"
        data.to_parquet(file_path, compression='snappy')
        self.metadata.add_entry(cache_key, file_path, 'timeframe', parent=m1_cache_key)
        return cache_key
```

**5.2.2 LRU Eviction System**
```python
# File: streamtrade/cache/lru_manager.py
class LRUCacheManager:
    def __init__(self, cache_dir: Path, max_size_gb: int):
        self.cache_dir = cache_dir
        self.max_size_gb = max_size_gb
        self.lru_tracker = LRUTracker(cache_dir / 'metadata' / 'lru_tracker.json')

    def check_and_evict(self):
        """Check cache size and evict LRU entries if needed."""
        current_size = self._calculate_cache_size()
        if current_size > self.max_size_gb:
            self._evict_lru_entries(current_size - self.max_size_gb)

    def _evict_lru_entries(self, size_to_free_gb: float):
        """Evict least recently used entries."""
        # Implementation with cascade deletion for indicators
```

#### **Phase 5.3: Advanced Indicator Cache (Sprint 7)**

**5.3.1 Per-Indicator Caching**
```python
# File: streamtrade/cache/indicator_cache.py
class IndicatorCache:
    def __init__(self, disk_cache: SmartDiskCache):
        self.disk_cache = disk_cache

    def store_indicator_calculation(self, indicator_name: str, params: dict,
                                  data_cache_key: str, result: pd.DataFrame) -> str:
        """Store indicator calculation results separately."""
        indicator_key = f"ind_{indicator_name}_{hash(str(params))}_{data_cache_key}"
        file_path = self.disk_cache.cache_dir / 'indicators' / f"{indicator_key}.parquet"
        result.to_parquet(file_path, compression='snappy')
        self.disk_cache.metadata.add_entry(indicator_key, file_path, 'indicator', parent=data_cache_key)
        return indicator_key

    def store_indicator_style(self, indicator_name: str, style_config: dict) -> str:
        """Store indicator style configuration separately."""
        style_key = f"style_{indicator_name}_{hash(str(style_config))}"
        file_path = self.disk_cache.cache_dir / 'styles' / f"{style_key}.json"
        with open(file_path, 'w') as f:
            json.dump(style_config, f)
        return style_key
```

**5.3.2 Cache Coherency System**
```python
# File: streamtrade/cache/coherency_manager.py
class CacheCoherencyManager:
    def __init__(self, disk_cache: SmartDiskCache):
        self.disk_cache = disk_cache

    def invalidate_data_cache(self, data_cache_key: str):
        """Invalidate data cache and all dependent indicators."""
        # Find all indicators that depend on this data
        dependent_indicators = self.disk_cache.metadata.find_dependents(data_cache_key)

        # Remove indicator caches
        for indicator_key in dependent_indicators:
            self.disk_cache.remove_entry(indicator_key)

        # Remove data cache
        self.disk_cache.remove_entry(data_cache_key)

    def update_indicator_only(self, indicator_name: str, new_params: dict, data_cache_key: str):
        """Update only specific indicator without affecting others."""
        # Remove old indicator cache
        old_indicator_keys = self.disk_cache.metadata.find_indicator_cache(indicator_name, data_cache_key)
        for key in old_indicator_keys:
            self.disk_cache.remove_entry(key)

        # Recalculate and cache new indicator
        # (Implementation will trigger indicator recalculation)
```

### 4.4 Expected Benefits

#### **Performance Improvements**
- **Disk cache persistence**: Data survives browser refresh
- **LRU eviction**: Automatic memory management
- **Per-indicator caching**: Only recalculate affected indicators
- **Session-aware conversion**: Accurate timeframe boundaries

#### **User Experience Improvements**
- **Predictable data loading**: "5 days back" vs "1000 candles"
- **Accurate timeframe conversion**: Proper session boundaries
- **Configurable behavior**: User control over caching and loading
- **Clear feedback**: Insufficient data warnings

#### **Technical Improvements**
- **Scalable architecture**: Disk-based caching supports large datasets
- **Modular design**: Separate concerns (data, indicators, styles)
- **Error recovery**: Graceful handling of corrupted cache
- **Extensible system**: Easy to add new features

### 4.5 Implementation Timeline

**Sprint 5 (Week 5): Foundation**
- User settings system
- Session-aware timeframe conversion
- N Days Back loading strategy
- Basic testing

**Sprint 6 (Week 6): Caching**
- Parquet-based disk cache
- LRU eviction system
- Cache persistence and recovery
- Performance optimization

**Sprint 7 (Week 7): Advanced Features**
- Per-indicator caching
- Style cache separation
- Cache coherency management
- Comprehensive testing and documentation

**Total Estimated Time**: 3 weeks (15-21 days)
**Complexity Level**: Advanced
**Risk Level**: Medium (well-defined requirements, proven technologies)
