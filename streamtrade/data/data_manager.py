"""
Main data manager for StreamTrade platform.
Coordinates data loading, caching, and timeframe conversion.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Tuple, Any
from datetime import datetime, timedelta
import threading
from pathlib import Path

from ..config.settings import settings
from ..config.logging_config import get_logger, log_performance
from ..core.utils import memory_usage, estimate_memory_usage
from .data_loader import DataLoader
from .timeframe_converter import TimeframeConverter

logger = get_logger(__name__)


class DataManager:
    """
    Central data management system for StreamTrade platform.
    
    Features:
    - Unified interface for data access
    - Intelligent caching system
    - Memory management
    - Multi-timeframe support
    - Thread-safe operations
    """
    
    def __init__(self):
        self.data_loader = DataLoader()
        self.timeframe_converter = TimeframeConverter()

        # Cache settings
        self.max_cache_size_mb = settings.performance_settings["max_memory_mb"] * 0.7  # 70% of max memory
        self.cache_timeout_minutes = settings.performance_settings["cache_timeout_minutes"]

        # Data cache: {cache_key: {'data': DataFrame, 'timestamp': datetime, 'size_mb': float}}
        self._data_cache = {}
        self._cache_lock = threading.Lock()

        # M1 base data cache for efficient timeframe switching
        self._m1_base_cache = {}  # {pair: {'data': DataFrame, 'timestamp': datetime, 'size_mb': float}}

        # Available pairs and their info
        self._pairs_info = {}
        self._refresh_pairs_info()

        logger.info("DataManager initialized with optimized timeframe switching")
    
    def _refresh_pairs_info(self):
        """Refresh information about available pairs."""
        try:
            for pair in settings.available_pairs:
                date_range = self.data_loader.get_available_date_range(pair)
                files_info = self.data_loader.discover_files(pair)
                
                self._pairs_info[pair] = {
                    'date_range': date_range,
                    'files_by_year': files_info,
                    'total_files': sum(len(files) for files in files_info.values())
                }
            
            logger.info(f"Refreshed info for {len(self._pairs_info)} pairs")
            
        except Exception as e:
            logger.error(f"Error refreshing pairs info: {str(e)}")
    
    def get_available_pairs(self) -> List[str]:
        """Get list of available currency pairs."""
        return list(self._pairs_info.keys())
    
    def get_pair_info(self, pair: str) -> Dict[str, Any]:
        """
        Get information about a specific pair.
        
        Args:
            pair: Currency pair
            
        Returns:
            Dictionary with pair information
        """
        return self._pairs_info.get(pair.upper(), {})
    
    def get_available_timeframes(self) -> List[str]:
        """Get list of supported timeframes."""
        return settings.data_settings["supported_timeframes"]
    
    @log_performance
    def get_data(
        self,
        pair: str,
        timeframe: str = "H1",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        max_candles: Optional[int] = None,
        use_cache: bool = True
    ) -> Optional[pd.DataFrame]:
        """
        Get OHLCV data for specified parameters.
        
        Args:
            pair: Currency pair (e.g., 'EURUSD')
            timeframe: Timeframe (M1, M5, H1, D1, etc.)
            start_date: Start date (optional)
            end_date: End date (optional)
            max_candles: Maximum number of candles (optional)
            use_cache: Whether to use caching
            
        Returns:
            DataFrame with OHLCV data or None if error
        """
        try:
            # Validate inputs
            pair = pair.upper()
            if not settings.validate_pair(pair):
                logger.error(f"Invalid pair: {pair}")
                return None
            
            if not settings.validate_timeframe(timeframe):
                logger.error(f"Invalid timeframe: {timeframe}")
                return None
            
            # Generate cache key
            cache_key = self._generate_cache_key(pair, timeframe, start_date, end_date, max_candles)
            
            # Check cache first
            if use_cache:
                cached_data = self._get_from_cache(cache_key)
                if cached_data is not None:
                    logger.debug(f"Cache hit for {pair} {timeframe}")
                    return cached_data
            
            # Try optimized loading first (check if we can use cached M1 data)
            if self._can_use_cached_m1(pair, timeframe, max_candles, start_date, end_date):
                logger.info(f"Using cached M1 data for {pair} {timeframe}")
                return self._convert_from_cached_m1(pair, timeframe, max_candles, start_date, end_date, cache_key)

            # Load M1 data from files
            logger.info(f"Loading M1 data from files for {pair} {timeframe}")

            # Calculate max_rows for M1 data if max_candles is specified
            max_rows = None
            if max_candles and timeframe != "M1":
                multiplier = settings.get_timeframe_multiplier(timeframe)
                max_rows = max_candles * multiplier * 2  # Buffer for safety
            elif max_candles:
                max_rows = max_candles

            # Load M1 data
            m1_data = self.data_loader.load_data_range(
                pair=pair,
                start_date=start_date,
                end_date=end_date,
                max_rows=max_rows
            )
            
            if m1_data is None or m1_data.empty:
                logger.warning(f"No M1 data available for {pair}")
                return None
            
            # Convert to target timeframe if needed
            if timeframe == "M1":
                result_data = m1_data
            else:
                result_data = self.timeframe_converter.convert_timeframe(
                    m1_data, 
                    timeframe, 
                    validate_input=True
                )
                
                if result_data is None:
                    logger.error(f"Failed to convert {pair} to {timeframe}")
                    return None
            
            # Apply max_candles limit if specified
            if max_candles and len(result_data) > max_candles:
                result_data = result_data.tail(max_candles)
                logger.debug(f"Limited to {max_candles} candles")
            
            # Cache the M1 base data for future timeframe switching
            if use_cache and m1_data is not None:
                self._cache_m1_base_data(pair, m1_data)

            # Cache the result
            if use_cache:
                self._add_to_cache(cache_key, result_data)

            logger.info(f"Successfully loaded {len(result_data)} {timeframe} candles for {pair}")
            return result_data
            
        except Exception as e:
            logger.error(f"Error getting data for {pair} {timeframe}: {str(e)}")
            return None
    
    def _generate_cache_key(
        self,
        pair: str,
        timeframe: str,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        max_candles: Optional[int]
    ) -> str:
        """Generate cache key for data request."""
        key_parts = [pair, timeframe]
        
        if start_date:
            key_parts.append(start_date.strftime("%Y%m%d"))
        if end_date:
            key_parts.append(end_date.strftime("%Y%m%d"))
        if max_candles:
            key_parts.append(str(max_candles))
        
        return "_".join(key_parts)
    
    def _get_from_cache(self, cache_key: str) -> Optional[pd.DataFrame]:
        """Get data from cache if available and not expired."""
        with self._cache_lock:
            if cache_key not in self._data_cache:
                return None
            
            cache_entry = self._data_cache[cache_key]
            
            # Check if cache entry is expired
            age_minutes = (datetime.now() - cache_entry['timestamp']).total_seconds() / 60
            if age_minutes > self.cache_timeout_minutes:
                del self._data_cache[cache_key]
                logger.debug(f"Cache entry expired: {cache_key}")
                return None
            
            return cache_entry['data'].copy()
    
    def _add_to_cache(self, cache_key: str, data: pd.DataFrame):
        """Add data to cache with memory management."""
        try:
            with self._cache_lock:
                # Estimate memory usage
                memory_info = estimate_memory_usage(data)
                data_size_mb = memory_info.get('total_mb', 0)
                
                # Check if we need to free up memory
                current_cache_size = sum(entry['size_mb'] for entry in self._data_cache.values())
                
                if current_cache_size + data_size_mb > self.max_cache_size_mb:
                    self._cleanup_cache(data_size_mb)
                
                # Add to cache
                self._data_cache[cache_key] = {
                    'data': data.copy(),
                    'timestamp': datetime.now(),
                    'size_mb': data_size_mb
                }
                
                logger.debug(f"Added to cache: {cache_key} ({data_size_mb:.1f} MB)")
                
        except Exception as e:
            logger.error(f"Error adding to cache: {str(e)}")

    def _can_use_cached_m1(self, pair: str, timeframe: str, max_candles: Optional[int],
                          start_date: Optional[datetime], end_date: Optional[datetime]) -> bool:
        """Check if we can use cached M1 data for the request."""
        if pair not in self._m1_base_cache:
            return False

        cached_entry = self._m1_base_cache[pair]

        # Check if cache is expired
        age_minutes = (datetime.now() - cached_entry['timestamp']).total_seconds() / 60
        if age_minutes > self.cache_timeout_minutes:
            del self._m1_base_cache[pair]
            return False

        cached_data = cached_entry['data']

        # Check if we have enough data for the request
        if max_candles and timeframe != "M1":
            multiplier = settings.get_timeframe_multiplier(timeframe)
            required_m1_rows = max_candles * multiplier
            if len(cached_data) < required_m1_rows:
                return False

        # Check date range coverage
        if start_date and cached_data.index[0] > start_date:
            return False
        if end_date and cached_data.index[-1] < end_date:
            return False

        return True

    def _convert_from_cached_m1(self, pair: str, timeframe: str, max_candles: Optional[int],
                               start_date: Optional[datetime], end_date: Optional[datetime],
                               cache_key: str) -> Optional[pd.DataFrame]:
        """Convert from cached M1 data to target timeframe."""
        try:
            cached_m1 = self._m1_base_cache[pair]['data']

            # Filter by date range if specified
            filtered_m1 = cached_m1
            if start_date:
                filtered_m1 = filtered_m1[filtered_m1.index >= start_date]
            if end_date:
                filtered_m1 = filtered_m1[filtered_m1.index <= end_date]

            # Convert to target timeframe
            if timeframe == "M1":
                result_data = filtered_m1
            else:
                result_data = self.timeframe_converter.convert_timeframe(
                    filtered_m1, timeframe, validate_input=True
                )

            if result_data is None:
                return None

            # Apply max_candles limit
            if max_candles and len(result_data) > max_candles:
                result_data = result_data.tail(max_candles)

            # Cache the result
            self._add_to_cache(cache_key, result_data)

            logger.debug(f"Converted from cached M1: {len(result_data)} {timeframe} candles")
            return result_data

        except Exception as e:
            logger.error(f"Error converting from cached M1: {str(e)}")
            return None

    def _cache_m1_base_data(self, pair: str, m1_data: pd.DataFrame):
        """Cache M1 base data for efficient timeframe switching."""
        try:
            with self._cache_lock:
                memory_info = estimate_memory_usage(m1_data)
                data_size_mb = memory_info.get('total_mb', 0)

                self._m1_base_cache[pair] = {
                    'data': m1_data.copy(),
                    'timestamp': datetime.now(),
                    'size_mb': data_size_mb
                }

                logger.debug(f"Cached M1 base data for {pair}: {len(m1_data)} rows ({data_size_mb:.1f} MB)")

        except Exception as e:
            logger.error(f"Error caching M1 base data: {str(e)}")

    def _cleanup_cache(self, required_mb: float):
        """Remove old cache entries to free up memory."""
        try:
            # Sort cache entries by timestamp (oldest first)
            sorted_entries = sorted(
                self._data_cache.items(),
                key=lambda x: x[1]['timestamp']
            )
            
            freed_mb = 0
            entries_removed = 0
            
            for cache_key, entry in sorted_entries:
                if freed_mb >= required_mb:
                    break
                
                freed_mb += entry['size_mb']
                del self._data_cache[cache_key]
                entries_removed += 1
            
            logger.debug(f"Cache cleanup: removed {entries_removed} entries, freed {freed_mb:.1f} MB")
            
        except Exception as e:
            logger.error(f"Error during cache cleanup: {str(e)}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._cache_lock:
            total_size_mb = sum(entry['size_mb'] for entry in self._data_cache.values())
            
            return {
                'entries': len(self._data_cache),
                'total_size_mb': total_size_mb,
                'max_size_mb': self.max_cache_size_mb,
                'usage_percent': (total_size_mb / self.max_cache_size_mb) * 100,
                'timeout_minutes': self.cache_timeout_minutes
            }
    
    def clear_cache(self):
        """Clear all cached data."""
        with self._cache_lock:
            self._data_cache.clear()
        
        # Also clear component caches
        self.data_loader.clear_cache()
        self.timeframe_converter.clear_cache()
        
        logger.info("All caches cleared")
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage information."""
        mem_info = memory_usage()
        cache_stats = self.get_cache_stats()
        
        return {
            'system_memory': mem_info,
            'cache_memory': cache_stats,
            'total_pairs': len(self._pairs_info)
        }
    
    def preload_data(
        self,
        pairs: List[str],
        timeframes: List[str],
        days_back: int = 30
    ):
        """
        Preload data for specified pairs and timeframes.
        
        Args:
            pairs: List of currency pairs
            timeframes: List of timeframes
            days_back: Number of days to preload
        """
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            total_combinations = len(pairs) * len(timeframes)
            loaded_count = 0
            
            logger.info(f"Preloading data for {total_combinations} combinations")
            
            for pair in pairs:
                for timeframe in timeframes:
                    try:
                        data = self.get_data(
                            pair=pair,
                            timeframe=timeframe,
                            start_date=start_date,
                            end_date=end_date,
                            use_cache=True
                        )
                        
                        if data is not None:
                            loaded_count += 1
                            logger.debug(f"Preloaded {pair} {timeframe}: {len(data)} candles")
                        
                    except Exception as e:
                        logger.warning(f"Failed to preload {pair} {timeframe}: {str(e)}")
            
            logger.info(f"Preloading completed: {loaded_count}/{total_combinations} successful")
            
        except Exception as e:
            logger.error(f"Error during preloading: {str(e)}")


# Global data manager instance
data_manager = DataManager()
