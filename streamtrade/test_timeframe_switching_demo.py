"""
Demo script to test timeframe switching optimization.
"""

import sys
from pathlib import Path

# Add the current directory to the path to import modules
sys.path.append(str(Path(__file__).parent))

from data.data_manager import DataManager
from visualization.chart_viewer import <PERSON><PERSON>iewer
from config.settings import settings


def demo_timeframe_switching():
    """Demonstrate the optimized timeframe switching functionality."""
    print("🚀 Lionaire Platform - Timeframe Switching Optimization Demo")
    print("=" * 60)
    
    # Initialize components
    data_manager = DataManager()
    chart_viewer = ChartViewer()
    
    test_pair = "EURUSD"
    
    print(f"\n📊 Testing with {test_pair}")
    print("-" * 30)
    
    # Step 1: Load initial data with specific candle count
    print("\n1️⃣ Loading initial data: 12 candles H1")
    success = chart_viewer.load_data(
        pair=test_pair,
        timeframe="H1",
        max_candles=12
    )
    
    if not success:
        print("❌ Failed to load initial data")
        return
    
    # Get data info
    data_info = chart_viewer.get_data_info()
    print(f"   ✅ Loaded: {data_info['candles_loaded']} candles")
    print(f"   📝 User context: {data_info['user_context']}")
    
    # Step 2: Switch to H4 timeframe
    print("\n2️⃣ Switching to H4 timeframe (should preserve 12 candles request)")
    chart_fig = chart_viewer.change_timeframe("H4")
    
    if chart_fig:
        data_info = chart_viewer.get_data_info()
        print(f"   ✅ H4 data: {data_info['candles_loaded']} candles")
        print(f"   📝 User context: {data_info['user_context']}")
    else:
        print("   ❌ Failed to switch to H4")
    
    # Step 3: Switch to M30 timeframe
    print("\n3️⃣ Switching to M30 timeframe (should use cached M1 data)")
    chart_fig = chart_viewer.change_timeframe("M30")
    
    if chart_fig:
        data_info = chart_viewer.get_data_info()
        print(f"   ✅ M30 data: {data_info['candles_loaded']} candles")
        print(f"   📝 User context: {data_info['user_context']}")
    else:
        print("   ❌ Failed to switch to M30")
    
    # Step 4: Switch to D1 timeframe (insufficient data scenario)
    print("\n4️⃣ Switching to D1 timeframe (may have insufficient data)")
    chart_fig = chart_viewer.change_timeframe("D1")
    
    if chart_fig:
        data_info = chart_viewer.get_data_info()
        print(f"   ✅ D1 data: {data_info['candles_loaded']} candles")
        print(f"   📝 User context: {data_info['user_context']}")
        
        if data_info['candles_loaded'] < 12:
            print(f"   ℹ️  Note: Only {data_info['candles_loaded']}/12 candles available (limited by data)")
    else:
        print("   ❌ Failed to switch to D1")
    
    # Step 5: Check cache statistics
    print("\n5️⃣ Cache Statistics")
    cache_stats = data_manager.get_cache_stats()
    print(f"   📊 Cache entries: {cache_stats['entries']}")
    print(f"   💾 Cache size: {cache_stats['total_size_mb']:.2f} MB")
    print(f"   📈 Cache usage: {cache_stats['usage_percent']:.2f}%")
    
    # Check M1 cache
    m1_cache_info = "Available" if test_pair in data_manager._m1_base_cache else "Not available"
    print(f"   🔄 M1 cache for {test_pair}: {m1_cache_info}")
    
    if test_pair in data_manager._m1_base_cache:
        m1_data_size = len(data_manager._m1_base_cache[test_pair]['data'])
        print(f"   📏 M1 cache size: {m1_data_size} candles")
    
    print("\n" + "=" * 60)
    print("✅ Demo completed successfully!")
    print("\n📋 Key Features Demonstrated:")
    print("   • User context preservation across timeframe switches")
    print("   • Efficient M1 cache utilization")
    print("   • Graceful handling of insufficient data")
    print("   • No unnecessary file reloading")
    print("   • Consistent candle count requests")


def demo_edge_cases():
    """Demonstrate edge cases in timeframe switching."""
    print("\n🔬 Testing Edge Cases")
    print("=" * 30)
    
    data_manager = DataManager()
    
    # Test 1: Very small candle count
    print("\n🧪 Test 1: Very small candle count (2 candles)")
    data = data_manager.get_data(
        pair="EURUSD",
        timeframe="H1",
        max_candles=2,
        preserve_user_request=True
    )
    
    if data is not None:
        print(f"   ✅ Loaded {len(data)} H1 candles")
        
        # Switch to D1 (should get very limited data)
        switched_data = data_manager.get_data_for_timeframe_switch(
            pair="EURUSD",
            new_timeframe="D1"
        )
        
        if switched_data is not None:
            print(f"   ✅ D1 switch: {len(switched_data)} candles")
        else:
            print("   ❌ D1 switch failed")
    
    # Test 2: Check user context persistence
    print("\n🧪 Test 2: User context persistence")
    user_context = data_manager.get_user_context("EURUSD")
    if user_context:
        print(f"   ✅ User context preserved: {user_context}")
    else:
        print("   ❌ User context lost")


if __name__ == "__main__":
    try:
        demo_timeframe_switching()
        demo_edge_cases()
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()
