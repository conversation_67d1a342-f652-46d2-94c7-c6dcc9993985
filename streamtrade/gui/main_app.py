"""
Main Streamlit application for Lionaire platform.
"""

import streamlit as st
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.config.logging_config import setup_logging, get_logger
from streamtrade.config.settings import settings
from streamtrade.config.strings import get_string, strings
from streamtrade.visualization.chart_viewer import ChartViewer
from streamtrade.gui.components.data_selector import DataSelector
from streamtrade.gui.components.chart_component import ChartComponent
from streamtrade.gui.components.indicator_panel import IndicatorPanel

# Setup logging
setup_logging(level="INFO", console_output=False)
logger = get_logger(__name__)


class LionaireApp:
    """
    Main Lionaire Streamlit application.

    Features:
    - Data selection and loading
    - Interactive chart display
    - Technical indicator management
    - Real-time chart updates
    - Export capabilities
    """
    
    def __init__(self):
        self.setup_page_config()
        self.initialize_components()
        
    def setup_page_config(self):
        """Configure Streamlit page settings."""
        st.set_page_config(
            page_title=settings.gui_settings["page_title"],
            page_icon=settings.gui_settings["page_icon"],
            layout=settings.gui_settings["layout"],
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS for better styling
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .metric-container {
            background-color: #f0f2f6;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 0.5rem 0;
        }
        
        .sidebar .sidebar-content {
            background-color: #f8f9fa;
        }
        
        .stButton > button {
            width: 100%;
        }
        
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 0.75rem;
            border-radius: 0.25rem;
            border: 1px solid #c3e6cb;
        }
        
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 0.75rem;
            border-radius: 0.25rem;
            border: 1px solid #f5c6cb;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def initialize_components(self):
        """Initialize application components."""
        try:
            # Initialize chart viewer (this will also initialize data manager)
            if 'chart_viewer' not in st.session_state:
                st.session_state.chart_viewer = ChartViewer()
            
            self.chart_viewer = st.session_state.chart_viewer
            
            # Initialize GUI components
            self.data_selector = DataSelector(self.chart_viewer)
            self.chart_component = ChartComponent(self.chart_viewer)
            self.indicator_panel = IndicatorPanel(self.chart_viewer)
            
            logger.info("StreamTrade app components initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing components: {str(e)}")
            st.error(f"Failed to initialize application: {str(e)}")
    
    def render_header(self):
        """Render application header."""
        st.markdown(f'<h1 class="main-header">{strings.APP["title"]}</h1>', unsafe_allow_html=True)

        st.markdown(f"""
        <div style="text-align: center; margin-bottom: 2rem; color: #666;">
            {strings.APP["subtitle"]}
        </div>
        """, unsafe_allow_html=True)
        
        # Statistics bar removed - information available in chart details below
    
    def render_sidebar(self):
        """Render sidebar with data selection and controls."""
        with st.sidebar:
            st.header("🎛️ Control Panel")
            
            # Data selection
            with st.expander("📊 Data Selection", expanded=True):
                self.data_selector.render()
            
            # Indicator management
            with st.expander("📈 Indicators", expanded=False):
                self.indicator_panel.render_indicator_summary()
                
                if st.button("🔧 Manage Indicators"):
                    st.session_state.show_indicator_panel = True
            
            # System information
            with st.expander("ℹ️ System Info", expanded=False):
                self.render_system_info()
            
            # Help section
            with st.expander("❓ Help", expanded=False):
                self.render_help()
    
    def render_main_content(self):
        """Render main content area."""
        # Check if indicator panel should be shown
        if st.session_state.get('show_indicator_panel', False):
            self.render_indicator_management()
        else:
            self.render_chart_view()
    
    def render_chart_view(self):
        """Render main chart view."""
        # Chart display
        fig = self.chart_component.render()
        
        # Chart controls and analysis
        if fig:
            col1, col2 = st.columns([1, 1])  # 50:50 ratio for equal width

            with col1:
                self.chart_component.render_chart_settings()

            with col2:
                self.chart_component.render_chart_analysis()
        
        # Data information
        if hasattr(st.session_state, 'data_loaded') and st.session_state.data_loaded:
            with st.expander("📋 Data Information", expanded=False):
                self.data_selector.render_data_info()
                self.data_selector.render_export_options()
    
    def render_indicator_management(self):
        """Render indicator management interface."""
        col1, col2 = st.columns([4, 1])

        with col1:
            st.subheader("📊 Indicator Management")  # Changed from header to subheader for alignment

        with col2:
            if st.button("⬅️ Back to Chart"):
                st.session_state.show_indicator_panel = False
                st.rerun()
        
        # Render indicator panel
        self.indicator_panel.render()
    
    def render_system_info(self):
        """Render system information."""
        try:
            from streamtrade.data.data_manager import data_manager
            
            # Memory usage
            cache_stats = data_manager.get_cache_stats()
            
            st.write("**Cache Information**")
            st.write(f"Entries: {cache_stats['entries']}")
            st.write(f"Size: {cache_stats['total_size_mb']:.2f} MB")
            st.write(f"Usage: {cache_stats['usage_percent']:.1f}%")
            
            # Application info
            st.write("**Application**")
            st.write(f"Version: 0.2.0")
            st.write(f"Phase: 2 (Visualization)")
            
            # Clear cache button
            if st.button("🗑️ Clear Cache"):
                data_manager.clear_cache()
                st.success("Cache cleared!")
                st.rerun()
        
        except Exception as e:
            st.error(f"Error getting system info: {str(e)}")
    
    def render_help(self):
        """Render help information."""
        st.write("**Quick Start:**")
        st.write("1. Select a currency pair and timeframe")
        st.write("2. Click 'Load Data' to fetch historical data")
        st.write("3. Add technical indicators using the indicator panel")
        st.write("4. Analyze the chart and export results")
        
        st.write("**Chart Interaction:**")
        st.write("• **Pan Chart**: Click and drag to move chart")
        st.write("• **Zoom**: Mouse wheel to zoom in/out")
        st.write("• **Price Scaling**: Drag price axis (right side) to scale prices")
        st.write("• **Reset View**: Double-click chart to reset zoom")
        st.write("• **Remove Gaps**: Use chart settings to remove weekend gaps")
        st.write("• **Fullscreen**: Use toolbar button for fullscreen view")
        
        st.write("**Support:**")
        st.write("• Check logs for detailed error information")
        st.write("• Clear cache if experiencing issues")
        st.write("• Refresh page to reset application state")
    
    def run(self):
        """Run the main application."""
        try:
            # Render header
            self.render_header()
            
            # Render sidebar
            self.render_sidebar()
            
            # Render main content
            self.render_main_content()
            
            # Footer
            st.markdown("---")
            st.markdown(
                f"<div style='text-align: center; color: #666; font-size: 0.8rem;'>"
                f"{strings.APP['footer']}"
                "</div>",
                unsafe_allow_html=True
            )
            
        except Exception as e:
            logger.error(f"Error in main app: {str(e)}")
            st.error(get_string("MESSAGES", "application_error", error=str(e)))

            # Show error details in expander
            with st.expander(get_string("MESSAGES", "error_details")):
                st.code(str(e))
                st.write(get_string("MESSAGES", "refresh_page"))


def main():
    """Main entry point for Streamlit app."""
    try:
        app = LionaireApp()
        app.run()
    except Exception as e:
        st.error(f"Failed to start application: {str(e)}")
        st.write("Please check your installation and try again.")


if __name__ == "__main__":
    main()
